import { <PERSON>, <PERSON>, BookOpen, Sparkles } from 'lucide-react';

const AboutSection = () => {
  return (
    <section id="about" className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="poetry-card p-8 md:p-12">
        <h2 className="poetry-title text-3xl md:text-4xl text-center mb-8 text-primary">
          More About Me
        </h2>
        
        <div className="royal-text text-lg leading-relaxed space-y-6 text-foreground">
          <p>
            Welcome to my little digital sanctuary! I'm <PERSON><PERSON><PERSON>, and this space is where I pour out 
            everything that swirls around in my head - from philosophical musings about happiness to 
            random 3 AM thoughts that somehow make perfect sense in the darkness.
          </p>
          
          <p>
            I believe in the power of words to heal, to connect, and to make sense of this beautifully 
            chaotic world we live in. Whether it's a haiku that captures a fleeting moment or a 
            rambling essay about the meaning of life, I'm here for all of it.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
            <div className="flex items-start space-x-3">
              <Coffee className="text-soft-orange mt-1" size={20} />
              <div>
                <h3 className="font-semibold text-primary mb-2">Fueled by Coffee</h3>
                <p className="text-sm text-muted-foreground">
                  Most of my best thoughts happen over a steaming cup of coffee, preferably while 
                  staring out a window.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <BookOpen className="text-baby-blue mt-1" size={20} />
              <div>
                <h3 className="font-semibold text-primary mb-2">Always Reading</h3>
                <p className="text-sm text-muted-foreground">
                  Books are my escape, my teachers, and my inspiration. Currently diving into 
                  philosophy and poetry.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Heart className="text-baby-purple mt-1" size={20} />
              <div>
                <h3 className="font-semibold text-primary mb-2">Hopeless Romantic</h3>
                <p className="text-sm text-muted-foreground">
                  I find beauty in ordinary moments and believe that love, in all its forms, 
                  is what makes life worth living.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Sparkles className="text-pastel-green mt-1" size={20} />
              <div>
                <h3 className="font-semibold text-primary mb-2">Eternal Optimist</h3>
                <p className="text-sm text-muted-foreground">
                  Even on the darkest days, I believe there's always something beautiful 
                  waiting to be discovered.
                </p>
              </div>
            </div>
          </div>
          
          <p>
            This blog is my attempt to share these discoveries with you. Some posts might make you 
            think, others might make you smile, and hopefully, a few will make you feel a little 
            less alone in this vast universe.
          </p>
          
          <p className="text-center font-semibold text-primary">
            Thank you for being here and for reading my words. ✨
          </p>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
