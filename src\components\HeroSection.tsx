import { Instagram, Pin } from 'lucide-react';

const HeroSection = () => {
  return (
    <section id="home" className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="poetry-card overflow-hidden">
        {/* Hero Image */}
        <div className="relative h-64 md:h-80 overflow-hidden">
          <img
            src="/src/images/hero image.jpg"
            alt="Hero"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          <div className="absolute bottom-6 left-6 text-white">
            <h1 className="poetry-title text-4xl md:text-5xl mb-2">More Than A Haiku</h1>
            <p className="royal-text text-lg">Kirthana's Digital Sanctuary</p>
          </div>
        </div>

        <div className="p-8 md:p-12 text-center">
          <h2 className="poetry-title text-3xl md:text-4xl mb-8 text-primary">
            About the Author
          </h2>
        
        <div className="royal-text text-lg md:text-xl leading-relaxed space-y-6 text-foreground">
          <p>
            Hi, I'm <span className="font-semibold text-primary">Kirthana</span> — professional optimist, 
            part-time daydreamer, and full-time collector of random thoughts. This blog is my little 
            corner of the internet where I untangle the chaos in my head and turn it into words and poems.
          </p>
          
          <p>
            Expect musings that range from mildly philosophical to mildly ridiculous, with the occasional 
            existential crisis thrown in for flair.
          </p>
          
          <p>
            When I'm not busy typing out mental spirals, I can usually be found overanalyzing movie endings, 
            romanticizing ordinary moments, or trying to remember why I walked into a room.
          </p>
          
          <p className="font-semibold">
            Stick around — you might just find a piece of your own thoughts hidden between mine.
          </p>
        </div>

        <div className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-6">
          <a
            href="https://instagram.com/kiki_writesromcoms"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-poetry gradient-blue flex items-center gap-2 px-6 py-3 text-primary font-medium"
          >
            <Instagram size={20} />
            @kiki_writesromcoms
          </a>
          
          <a
            href="https://in.pinterest.com/kirthananandha/?invite_code=e29c25fa3008460f8e51880c40dd8c8c&sender=633389272501628586"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-poetry gradient-purple flex items-center gap-2 px-6 py-3 text-primary font-medium"
          >
            <Pin size={20} />
            Pinterest
          </a>
        </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;