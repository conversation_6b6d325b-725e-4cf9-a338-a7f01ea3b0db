import Navigation from '../components/Navigation';
import HeroSection from '../components/HeroSection';
import PoemCards from '../components/PoemCards';
import ThoughtsCards from '../components/ThoughtsCards';
import AboutSection from '../components/AboutSection';
import ContactSection from '../components/ContactSection';

const Index = () => {
  return (
    <div className="min-h-screen">
      <Navigation />
      <main>
        <HeroSection />
        <PoemCards />
        <ThoughtsCards />
        <AboutSection />
        <ContactSection />
        
        {/* Footer */}
        <footer className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="poetry-card p-8 text-center">
            <p className="royal-text text-muted-foreground mb-4">
              "More than a haiku, less than a novel, exactly what it needs to be."
            </p>
            <p className="royal-text text-sm text-muted-foreground">
              © 2024 Kirthana. All thoughts and musings are mine, typos are gifts.
            </p>
          </div>
        </footer>
      </main>
    </div>
  );
};

export default Index;
