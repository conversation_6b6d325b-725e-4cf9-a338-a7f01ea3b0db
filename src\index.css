@tailwind base;
@tailwind components;
@tailwind utilities;

/* Poetry blog design system - More Than A Haiku
Colors: baby blue, baby purple, soft orange, pastel green on beige notebook paper
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Notebook paper background */
    --background: 45 25% 94%; /* soft beige */
    --foreground: 25 20% 25%; /* warm dark brown for text */

    /* Card backgrounds with soft shadows */
    --card: 45 30% 96%; /* slightly lighter beige */
    --card-foreground: 25 20% 25%;

    --popover: 45 30% 96%;
    --popover-foreground: 25 20% 25%;

    /* Poetry accent colors */
    --primary: 25 20% 25%; /* warm dark brown */
    --primary-foreground: 45 30% 96%;

    --secondary: 45 30% 96%;
    --secondary-foreground: 25 20% 25%;

    --muted: 45 20% 92%;
    --muted-foreground: 25 15% 45%;

    --accent: 45 30% 96%;
    --accent-foreground: 25 20% 25%;

    /* Poetry theme colors */
    --baby-blue: 200 75% 85%;
    --baby-purple: 280 60% 85%;
    --soft-orange: 35 80% 80%;
    --pastel-green: 120 50% 80%;

    /* Gradients for poetry cards */
    --gradient-blue: linear-gradient(135deg, hsl(200 75% 90%), hsl(200 75% 85%));
    --gradient-purple: linear-gradient(135deg, hsl(280 60% 90%), hsl(280 60% 85%));
    --gradient-orange: linear-gradient(135deg, hsl(35 80% 85%), hsl(35 80% 80%));
    --gradient-green: linear-gradient(135deg, hsl(120 50% 85%), hsl(120 50% 80%));

    /* Shadows for floating elements */
    --shadow-soft: 0 4px 16px hsl(25 20% 25% / 0.1);
    --shadow-card: 0 8px 32px hsl(25 20% 25% / 0.12);
    --shadow-hover: 0 12px 40px hsl(25 20% 25% / 0.15);

    /* Notebook lines */
    --line-color: hsl(25 15% 88%);

    --destructive: 0 65% 70%;
    --destructive-foreground: 45 30% 96%;

    --border: 25 15% 88%;
    --input: 45 20% 92%;
    --ring: 200 75% 75%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-medium;
    font-family: 'Crimson Text', 'Times New Roman', serif;
    font-style: italic;
    /* Notebook grid pattern with animated doodles */
    background-image:
      linear-gradient(var(--line-color) 1px, transparent 1px),
      linear-gradient(90deg, var(--line-color) 1px, transparent 1px);
    background-size: 24px 24px;
    background-position: 0 0;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
  }

  /* Animated floating doodles */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    background-image:
      radial-gradient(circle at 20% 20%, hsl(var(--baby-blue)) 2px, transparent 2px),
      radial-gradient(circle at 80% 30%, hsl(var(--soft-orange)) 1.5px, transparent 1.5px),
      radial-gradient(circle at 40% 70%, hsl(var(--baby-purple)) 2.5px, transparent 2.5px),
      radial-gradient(circle at 90% 80%, hsl(var(--pastel-green)) 1px, transparent 1px),
      radial-gradient(circle at 10% 90%, hsl(var(--baby-blue)) 1.8px, transparent 1.8px);
    background-size: 200px 200px, 150px 150px, 180px 180px, 120px 120px, 160px 160px;
    animation: floatDoodles 20s ease-in-out infinite;
  }

  @keyframes floatDoodles {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.3;
    }
    25% {
      transform: translateY(-10px) rotate(1deg);
      opacity: 0.5;
    }
    50% {
      transform: translateY(-5px) rotate(-1deg);
      opacity: 0.4;
    }
    75% {
      transform: translateY(-15px) rotate(0.5deg);
      opacity: 0.6;
    }
  }
}

@layer components {
  /* Floating card effect with enhanced animations */
  .poetry-card {
    @apply bg-card rounded-lg shadow-[var(--shadow-card)] transition-all duration-500 hover:shadow-[var(--shadow-hover)];
    position: relative;
    z-index: 10;
    transform: translateY(0px) rotate(0deg);
    animation: gentleFloat 6s ease-in-out infinite;
  }

  .poetry-card:hover {
    transform: translateY(-8px) scale(1.02);
    animation-play-state: paused;
  }

  .poetry-card:nth-child(odd) {
    animation-delay: -2s;
  }

  .poetry-card:nth-child(even) {
    animation-delay: -4s;
  }

  @keyframes gentleFloat {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-3px) rotate(0.5deg);
    }
    66% {
      transform: translateY(-1px) rotate(-0.3deg);
    }
  }

  /* Enhanced gradient variants for poetry cards with sparkle effects */
  .gradient-blue {
    background: var(--gradient-blue);
    position: relative;
    overflow: hidden;
  }

  .gradient-blue::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, hsl(var(--baby-blue) / 0.3) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: sparkle 8s linear infinite;
    pointer-events: none;
  }

  .gradient-purple {
    background: var(--gradient-purple);
    position: relative;
    overflow: hidden;
  }

  .gradient-purple::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, hsl(var(--baby-purple) / 0.3) 1px, transparent 1px);
    background-size: 25px 25px;
    animation: sparkle 10s linear infinite reverse;
    pointer-events: none;
  }

  .gradient-orange {
    background: var(--gradient-orange);
    position: relative;
    overflow: hidden;
  }

  .gradient-orange::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, hsl(var(--soft-orange) / 0.3) 1px, transparent 1px);
    background-size: 18px 18px;
    animation: sparkle 12s linear infinite;
    pointer-events: none;
  }

  .gradient-green {
    background: var(--gradient-green);
    position: relative;
    overflow: hidden;
  }

  .gradient-green::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, hsl(var(--pastel-green) / 0.3) 1px, transparent 1px);
    background-size: 22px 22px;
    animation: sparkle 9s linear infinite reverse;
    pointer-events: none;
  }

  @keyframes sparkle {
    0% {
      transform: translateX(0) translateY(0) rotate(0deg);
      opacity: 0.3;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      transform: translateX(-20px) translateY(-20px) rotate(360deg);
      opacity: 0.3;
    }
  }

  /* Royal italic text styling with subtle animation */
  .royal-text {
    @apply font-medium;
    font-family: 'Crimson Text', 'Times New Roman', serif;
    font-style: italic;
    letter-spacing: 0.02em;
    position: relative;
  }

  .royal-text:hover {
    animation: textShimmer 2s ease-in-out;
  }

  @keyframes textShimmer {
    0%, 100% {
      text-shadow: 0 0 0 transparent;
    }
    50% {
      text-shadow: 0 0 8px hsl(var(--baby-blue) / 0.3);
    }
  }

  /* Poetry title styling with enhanced effects */
  .poetry-title {
    @apply royal-text text-2xl font-bold;
    text-shadow: 0 1px 2px hsl(25 20% 25% / 0.1);
    position: relative;
  }

  .poetry-title:hover {
    animation: titleGlow 1.5s ease-in-out;
  }

  @keyframes titleGlow {
    0%, 100% {
      text-shadow: 0 1px 2px hsl(25 20% 25% / 0.1);
    }
    50% {
      text-shadow: 0 1px 2px hsl(25 20% 25% / 0.1), 0 0 20px hsl(var(--soft-orange) / 0.4);
    }
  }

  /* Soft button styles */
  .btn-poetry {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
    @apply bg-card text-card-foreground shadow-[var(--shadow-soft)] hover:shadow-[var(--shadow-card)] hover:-translate-y-0.5;
    @apply px-4 py-2;
  }

  /* Navigation styles */
  .nav-link {
    @apply royal-text relative px-4 py-2 rounded-md transition-all duration-300;
    @apply hover:bg-accent hover:shadow-[var(--shadow-soft)];
  }

  .nav-link.active {
    @apply bg-accent shadow-[var(--shadow-soft)];
  }

  /* Animated underline for links */
  .underline-animation {
    @apply relative;
  }

  .underline-animation::after {
    @apply content-[''] absolute bottom-0 left-0 w-0 h-0.5 bg-ring transition-all duration-300;
  }

  .underline-animation:hover::after {
    @apply w-full;
  }

  /* Floating decorative elements */
  .floating-hearts {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
  }

  .floating-hearts::before,
  .floating-hearts::after {
    content: '♡ ✧ ❀ ✦ ♡ ✧ ❀ ✦';
    position: absolute;
    font-size: 12px;
    color: hsl(var(--baby-purple) / 0.4);
    animation: floatUp 15s linear infinite;
    white-space: nowrap;
  }

  .floating-hearts::before {
    left: 10%;
    animation-delay: -5s;
  }

  .floating-hearts::after {
    left: 80%;
    animation-delay: -10s;
    color: hsl(var(--pastel-green) / 0.4);
  }

  @keyframes floatUp {
    0% {
      transform: translateY(100vh) rotate(0deg);
      opacity: 0;
    }
    10% {
      opacity: 0.6;
    }
    90% {
      opacity: 0.6;
    }
    100% {
      transform: translateY(-100px) rotate(360deg);
      opacity: 0;
    }
  }

  /* Image cropping and fitting styles */
  .poetry-card img,
  .hero-image {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
    transition: transform 0.5s ease;
  }

  .poetry-card:hover img {
    transform: scale(1.05);
  }

  /* Pulse animation for interactive elements */
  .pulse-on-hover:hover {
    animation: gentlePulse 1s ease-in-out;
  }

  @keyframes gentlePulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.02);
    }
  }
}