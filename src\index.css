@tailwind base;
@tailwind components;
@tailwind utilities;

/* Poetry blog design system - More Than A Haiku
Colors: baby blue, baby purple, soft orange, pastel green on beige notebook paper
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Warm notebook paper background - cream/faded yellow */
    --background: 48 35% 92%; /* warm cream with yellow undertones */
    --foreground: 30 25% 20%; /* warm dark brown for text */

    /* Card backgrounds with soft shadows */
    --card: 48 40% 95%; /* slightly lighter warm cream */
    --card-foreground: 30 25% 20%;

    --popover: 45 30% 96%;
    --popover-foreground: 25 20% 25%;

    /* Poetry accent colors */
    --primary: 30 25% 20%; /* warm dark brown */
    --primary-foreground: 48 40% 95%;

    --secondary: 48 40% 95%;
    --secondary-foreground: 30 25% 20%;

    --muted: 48 25% 88%;
    --muted-foreground: 30 20% 40%;

    --accent: 48 40% 95%;
    --accent-foreground: 30 25% 20%;

    /* Poetry theme colors */
    --baby-blue: 200 75% 85%;
    --baby-purple: 280 60% 85%;
    --soft-orange: 35 80% 80%;
    --pastel-green: 120 50% 80%;

    /* Gradients for poetry cards */
    --gradient-blue: linear-gradient(135deg, hsl(200 75% 90%), hsl(200 75% 85%));
    --gradient-purple: linear-gradient(135deg, hsl(280 60% 90%), hsl(280 60% 85%));
    --gradient-orange: linear-gradient(135deg, hsl(35 80% 85%), hsl(35 80% 80%));
    --gradient-green: linear-gradient(135deg, hsl(120 50% 85%), hsl(120 50% 80%));

    /* Notebook shadows */
    --shadow-soft: 0 2px 4px hsl(30 25% 20% / 0.08);
    --shadow-card: 0 4px 8px hsl(30 25% 20% / 0.12);
    --shadow-hover: 0 8px 16px hsl(30 25% 20% / 0.16);

    /* Notebook grid lines - soft brown-grey */
    --grid-line-color: hsl(35 20% 75%);
    --paper-texture-color: hsl(45 30% 88%);

    --destructive: 0 65% 70%;
    --destructive-foreground: 45 30% 96%;

    --border: 25 15% 88%;
    --input: 45 20% 92%;
    --ring: 200 75% 75%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    background-color: #fdf6e3; /* Creamy faded yellow tone */
    background-image:
      linear-gradient(to right, rgba(150, 130, 100, 0.08) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(150, 130, 100, 0.08) 1px, transparent 1px),
      url('https://www.transparenttextures.com/patterns/paper-fibers.png'); /* Optional paper texture */
    background-size: 40px 40px, 40px 40px, cover;
    background-repeat: repeat;
    background-attachment: fixed;
    background-position: top left, top left, center;
    color: #333;
    font-family: 'Merriweather', serif;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
  }


}

@layer components {
  /* Floating card effect */
  .poetry-card {
    @apply bg-card rounded-lg shadow-[var(--shadow-card)] transition-all duration-300 hover:shadow-[var(--shadow-hover)] hover:-translate-y-1;
  }

  /* Gradient variants for poetry cards */
  .gradient-blue {
    background: var(--gradient-blue);
  }

  .gradient-purple {
    background: var(--gradient-purple);
  }

  .gradient-orange {
    background: var(--gradient-orange);
  }

  .gradient-green {
    background: var(--gradient-green);
  }

  /* Royal italic text styling */
  .royal-text {
    @apply font-medium;
    font-family: 'Crimson Text', 'Times New Roman', serif;
    font-style: italic;
    letter-spacing: 0.02em;
  }

  /* Poetry title styling */
  .poetry-title {
    @apply royal-text text-2xl font-bold;
    text-shadow: 0 1px 2px hsl(25 20% 25% / 0.1);
  }

  /* Soft button styles */
  .btn-poetry {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
    @apply bg-card text-card-foreground shadow-[var(--shadow-soft)] hover:shadow-[var(--shadow-card)] hover:-translate-y-0.5;
    @apply px-4 py-2;
  }

  /* Navigation styles */
  .nav-link {
    @apply royal-text relative px-4 py-2 rounded-md transition-all duration-300;
    @apply hover:bg-accent hover:shadow-[var(--shadow-soft)];
  }

  .nav-link.active {
    @apply bg-accent shadow-[var(--shadow-soft)];
  }

  /* Animated underline for links */
  .underline-animation {
    @apply relative;
  }

  .underline-animation::after {
    @apply content-[''] absolute bottom-0 left-0 w-0 h-0.5 bg-ring transition-all duration-300;
  }

  .underline-animation:hover::after {
    @apply w-full;
  }

  /* Cute static doodles in viewport corners */
  .cute-doodles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
  }

  .cute-doodles::before {
    content: '✧ ❀ ♡';
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 16px;
    color: hsl(var(--baby-purple) / 0.6);
    letter-spacing: 8px;
  }

  .cute-doodles::after {
    content: '❀ ✦ ♡ ✧';
    position: absolute;
    bottom: 30px;
    left: 30px;
    font-size: 14px;
    color: hsl(var(--pastel-green) / 0.5);
    letter-spacing: 6px;
  }
}