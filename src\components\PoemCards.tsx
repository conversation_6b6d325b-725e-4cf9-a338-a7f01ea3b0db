import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Star, Sun, <PERSON>, <PERSON>, <PERSON>, Sparkles } from 'lucide-react';

interface Poem {
  id: string;
  title: string;
  preview: string;
  content: string;
  mood: 'blue' | 'purple' | 'orange' | 'green';
  icon: React.ReactNode;
  imageUrl: string;
}

const samplePoems: Poem[] = [
  
  {
    id: '5',
    title: 'New Land',
    preview: 'When I smile, she\'s creeping around, I close my eyes to sleep and that\'s when she speaks...',
    content: `When I smile, she's creeping around
I close my eyes to sleep and that's when she speaks
When I try to climb, she pulls me down
I float with ease but she makes me drown

When I look forward to the day, she brings up my faults
I dream about the future, and she reminds me of my past
When I make a mistake, she stands tall
I want to perform, but she makes me slip and fall

When I tried to find her, she disappeared
I gave up, and that's when she came
When I looked closely, I realised she was a darker reflection of me
I was so obstinate that ,the truth I failed to see

And then I suddenly realised
The hand holding the shadow was mine
I could let go but I chose not
And so I was left there to rot

But today is a new dawn,
My final chance to be gone
And as I left the gloomy hand,
I felt myself floating to a new land`,
    mood: 'purple',
    icon: <Star size={24} />,
    imageUrl: '/src/images/New Land.jpg'
  },
  {
    id: '6',
    title: 'Vault of Heaven',
    preview: 'What is beyond the azure? I ask thee, Is it really where one would find the gods and angels?',
    content: `What is beyond the azure?
I ask thee
Is it really where one would find..
The gods and angels?
Mother told me
She told me about the heaven
She said that's where we all go
After we end our lives on earth
She said it would be peaceful
And that I could float on the clouds
But I am still not very certain
So tell me
Do you think it really is the heaven?

The heaven? I'm not so sure
I do not believe in afterlife
If not afterlife where do you think we go?
I think our souls come back
Back to this earth
But in a different body
You see it's the circle
The circle?
Yes, the circle of life.
We die, pass on our souls
And shortly are born again
So its endless eh?
I don't know that, but maybe

What do you think Beth?
I don't believe in heaven
Simply because I don't believe in god
And I really don't hope to come back here
To this planet
Why do you say that Beth?
Thy leads a good life,
I doth been abandoned by god
And who needs him anyway
Don't say that Beth
Why not? it's true
My mother says heaven is for us all
Beth, i'm sure there's extra room for you
I don't know
I will save a seat for you right next to mine
I promise`,
    mood: 'blue',
    icon: <Sun size={24} />,
    imageUrl: '/src/images/vault of heaven.jpg'
  },
  {
    id: '7',
    title: 'Rebirth',
    preview: 'My soul is here again, My eyes are searching for yours, My hands are smaller this time...',
    content: `My soul is here again

My eyes are searching for yours

My hands are smaller this time

My hair is blonder

Will you be able to

Recognise my sky blue eyes

Will you be able to

See through my darker skin

Our bodies never mattered

Just the soul inside

Our worlds farther than ever

Just that love finds the other

They say soulmates aren't real

But they don't know you

Like I have, like I do

In the darkness of tonight,

Can you see my light?

Golden and lilac like

I see you coming back to me

One touch and we know

You're the one for me

Like you have been

Like you will be

Forever since ever, you and me`,
    mood: 'green',
    icon: <Heart size={24} />,
    imageUrl: '/src/images/rebirth.jpg'
  },
  {
    id: '8',
    title: 'Red String',
    preview: 'If I cut the strings that tie us together, They\'ll just tie right back...',
    content: `If I cut the strings that tie us together

They'll just tie right back

Because we can't be separated

It would take a lot more than that

If I pulled too hard

Hard enough to break our ties

You would catch my string

And scour through my lies

Because only a lie

Would make me pull away

From your love, your heart

And your kind pray

If you ripped your string

Off of mine

I'd beg and plead

To reverse the time

But you'd realise

How life isn't worth living

Without my words

Giving you meaning

If the strings are torn apart

Under the storms of time

We'd make sure to make it

Even if we aren't side by side

One day the storm will pass

Our strings would find each other

And this time we would last

Till the end of forever`,
    mood: 'orange',
    icon: <Heart size={24} />,
    imageUrl: '/src/images/red string.jpg'
  },
  {
    id: '9',
    title: 'Chronicles of Everland',
    preview: 'I have only heard of the fantasies of the land, The sweet fairy tales that one could have...',
    content: `I have only heard

Of the fantasies of the land

The sweet fairy tales

That one could have

The thrilling adventure

And of course the villains

The victory of Prince

And the girl in his arms

Why for does the girl not win

But only embrace the victor

She is allowed only to play

The damsel in distress

The one who must be saved

I know not a reason

Why she cannot be made to fight

How hard could a battle be?

For she fights one every day

A Battle against the world

for not thinking she is enough

And a battle against herself

For being born that way

A girl is much too less for battle

But what about the woman inside her

She endures the highest of pain

In labour,

though abuse, and hate

But no, this won't suffice

For a woman is a woman

She can only be cinderella

And wait for Prince Charming

To save her from evils

Only to subject her to another kind`,
    mood: 'purple',
    icon: <BookOpen size={24} />,
    imageUrl: '/src/images/chronicles of everland.jpg'
  }
];

const PoemCards = () => {
  const [selectedPoem, setSelectedPoem] = useState<Poem | null>(null);

  const openPoem = (poem: Poem) => {
    setSelectedPoem(poem);
  };

  const closePoem = () => {
    setSelectedPoem(null);
  };

  const getGradientClass = (mood: string) => {
    switch (mood) {
      case 'blue': return 'gradient-blue';
      case 'purple': return 'gradient-purple';
      case 'orange': return 'gradient-orange';
      case 'green': return 'gradient-green';
      default: return 'gradient-blue';
    }
  };

  return (
    <section id="poems" className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <h2 className="poetry-title text-3xl md:text-4xl text-center mb-12 text-primary">
        Poems & Thoughts
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {samplePoems.map((poem) => (
          <div
            key={poem.id}
            className={`poetry-card cursor-pointer group overflow-hidden pulse-on-hover ${getGradientClass(poem.mood)}`}
            onClick={() => openPoem(poem)}
          >
            <div className="relative h-48 overflow-hidden">
              <img
                src={poem.imageUrl}
                alt={poem.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
              <div className="absolute bottom-4 left-4 text-white">
                {poem.icon}
              </div>
            </div>
            
            <div className="p-6">
              <h3 className="poetry-title text-xl mb-3 text-primary">
                {poem.title}
              </h3>
              <p className="royal-text text-foreground line-clamp-3 leading-relaxed">
                {poem.preview}
              </p>
              <div className="mt-4 flex items-center text-sm text-muted-foreground">
                <BookOpen size={16} className="mr-2" />
                Click to read more
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Poem Modal */}
      {selectedPoem && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="poetry-card max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="relative">
              <img
                src={selectedPoem.imageUrl}
                alt={selectedPoem.title}
                className="w-full h-64 object-cover"
              />
              <button
                onClick={closePoem}
                className="absolute top-4 right-4 btn-poetry bg-white/90 hover:bg-white text-primary w-10 h-10 flex items-center justify-center"
              >
                ×
              </button>
            </div>
            
            <div className="p-8">
              <h3 className="poetry-title text-2xl md:text-3xl mb-6 text-primary">
                {selectedPoem.title}
              </h3>
              <div className="royal-text text-lg leading-relaxed whitespace-pre-line text-foreground">
                {selectedPoem.content}
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default PoemCards;