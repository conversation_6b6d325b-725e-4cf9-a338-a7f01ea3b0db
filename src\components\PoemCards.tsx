import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>rkles } from 'lucide-react';

interface Poem {
  id: string;
  title: string;
  preview: string;
  content: string;
  mood: 'blue' | 'purple' | 'orange' | 'green';
  icon: React.ReactNode;
  imageUrl: string;
}

const samplePoems: Poem[] = [
  {
    id: '1',
    title: 'Morning Coffee Thoughts',
    preview: 'Steam rises from my cup like prayers to the ceiling...',
    content: `Steam rises from my cup like prayers to the ceiling,
While I sit here, barely feeling,
The world wake up around me.

Coffee bitter, thoughts are sweeter,
Morning light makes everything clearer,
Or maybe it's just caffeine talking.

But in this quiet morning hour,
I find a strange and gentle power,
To face whatever comes my way.`,
    mood: 'orange',
    icon: <Coffee size={24} />,
    imageUrl: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=600&fit=crop&crop=center'
  },
  {
    id: '2',
    title: 'Midnight Musings',
    preview: 'The clock strikes twelve and thoughts emerge...',
    content: `The clock strikes twelve and thoughts emerge,
From corners of my mind,
Like shadows dancing on the wall,
They leave the day behind.

In darkness, truth feels sharper,
Words flow like midnight rain,
These hours between sleep and wake,
Are when I feel most sane.`,
    mood: 'blue',
    icon: <Moon size={24} />,
    imageUrl: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=600&fit=crop&crop=center'
  },
  {
    id: '3',
    title: 'Existential Tuesday',
    preview: 'Why do we call it Tuesday when it feels like forever?',
    content: `Why do we call it Tuesday when it feels like forever?
This arbitrary name for time,
As if labeling the endless spiral
Makes it easier to climb.

I wonder if days get tired
Of being the same day,
Over and over, Tuesday,
Never getting to be Wednesday.

But then I remember:
Time is just a story we tell,
And every Tuesday is different,
If we listen well.`,
    mood: 'purple',
    icon: <Sparkles size={24} />,
    imageUrl: 'https://images.unsplash.com/photo-1514539079130-25950c84af65?w=400&h=600&fit=crop&crop=center'
  },
  {
    id: '4',
    title: 'Garden of Thoughts',
    preview: 'I planted words in the soil of my mind...',
    content: `I planted words in the soil of my mind,
Watered them with wonder,
Watched them grow into poems,
That bloom like spring thunder.

Some flowers are questions,
Others, quiet statements,
All of them reaching toward light,
In their own arrangements.

This garden needs tending,
But grows wild and free,
A beautiful contradiction,
Just like me.`,
    mood: 'green',
    icon: <Flower size={24} />,
    imageUrl: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=600&fit=crop&crop=center'
  }
];

const PoemCards = () => {
  const [selectedPoem, setSelectedPoem] = useState<Poem | null>(null);

  const openPoem = (poem: Poem) => {
    setSelectedPoem(poem);
  };

  const closePoem = () => {
    setSelectedPoem(null);
  };

  const getGradientClass = (mood: string) => {
    switch (mood) {
      case 'blue': return 'gradient-blue';
      case 'purple': return 'gradient-purple';
      case 'orange': return 'gradient-orange';
      case 'green': return 'gradient-green';
      default: return 'gradient-blue';
    }
  };

  return (
    <section id="poems" className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <h2 className="poetry-title text-3xl md:text-4xl text-center mb-12 text-primary">
        Poems & Thoughts
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {samplePoems.map((poem) => (
          <div
            key={poem.id}
            className={`poetry-card cursor-pointer group overflow-hidden ${getGradientClass(poem.mood)}`}
            onClick={() => openPoem(poem)}
          >
            <div className="relative h-48 overflow-hidden">
              <img
                src={poem.imageUrl}
                alt={poem.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
              <div className="absolute bottom-4 left-4 text-white">
                {poem.icon}
              </div>
            </div>
            
            <div className="p-6">
              <h3 className="poetry-title text-xl mb-3 text-primary">
                {poem.title}
              </h3>
              <p className="royal-text text-foreground line-clamp-3 leading-relaxed">
                {poem.preview}
              </p>
              <div className="mt-4 flex items-center text-sm text-muted-foreground">
                <BookOpen size={16} className="mr-2" />
                Click to read more
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Poem Modal */}
      {selectedPoem && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="poetry-card max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="relative">
              <img
                src={selectedPoem.imageUrl}
                alt={selectedPoem.title}
                className="w-full h-64 object-cover"
              />
              <button
                onClick={closePoem}
                className="absolute top-4 right-4 btn-poetry bg-white/90 hover:bg-white text-primary w-10 h-10 flex items-center justify-center"
              >
                ×
              </button>
            </div>
            
            <div className="p-8">
              <h3 className="poetry-title text-2xl md:text-3xl mb-6 text-primary">
                {selectedPoem.title}
              </h3>
              <div className="royal-text text-lg leading-relaxed whitespace-pre-line text-foreground">
                {selectedPoem.content}
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default PoemCards;