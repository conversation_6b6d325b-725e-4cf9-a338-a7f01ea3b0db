import { Mail, MessageCircle, Heart } from 'lucide-react';

const ContactSection = () => {
  return (
    <section id="contact" className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="poetry-card p-8 md:p-12 text-center">
        <h2 className="poetry-title text-3xl md:text-4xl mb-8 text-primary">
          Let's Connect
        </h2>
        
        <div className="royal-text text-lg leading-relaxed space-y-6 text-foreground">
          <p>
            I love hearing from fellow dreamers, thinkers, and anyone who finds beauty in words. 
            Whether you want to share your thoughts on a post, discuss philosophy over virtual coffee, 
            or just say hello - I'm all ears!
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 my-8">
            <a 
              href="mailto:<EMAIL>" 
              className="flex items-center space-x-3 poetry-card p-4 hover:shadow-lg transition-all duration-300 text-primary hover:text-soft-orange"
            >
              <Mail size={24} />
              <span className="font-semibold">Drop me an email</span>
            </a>
            
            <div className="flex items-center space-x-3 poetry-card p-4 text-muted-foreground">
              <MessageCircle size={24} />
              <span className="font-semibold">More ways to connect coming soon!</span>
            </div>
          </div>
          
          <p className="text-muted-foreground">
            I try to respond to all messages, though sometimes it might take a little while - 
            I like to give thoughtful responses the time they deserve.
          </p>
          
          <div className="flex items-center justify-center space-x-2 text-primary">
            <Heart size={20} />
            <span className="font-semibold">Thank you for being part of this journey</span>
            <Heart size={20} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
