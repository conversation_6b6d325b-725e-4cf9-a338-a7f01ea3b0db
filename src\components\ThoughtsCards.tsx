import { useState } from 'react';
import { Book<PERSON><PERSON>, Calendar, User, MessageCircle, Lightbulb, Heart } from 'lucide-react';

interface Thought {
  id: string;
  title: string;
  date: string;
  preview: string;
  content: string;
  mood: 'blue' | 'purple' | 'orange' | 'green';
  icon: React.ReactNode;
  imageUrl: string;
  readTime: string;
}

const sampleThoughts: Thought[] = [
  {
    id: '1',
    title: 'What is happiness?',
    date: 'April 25, 2025',
    preview: 'This is me taking a break from poems. Let\'s get a little philosophical here. So what exactly do we mean by happiness and where does one find it?',
    content: `This is me taking a break from poems. Let's get a little philosophical here. So what exactly do we mean by happiness and where does one find it? Now the answer to that question is probably just as complicated and trying to find the purpose of one's life. And for the most part both those super hard questions are linked to each other.

Now although these questions are really hard to answer and many people fail to find answers, I urge you all to try and find answers to them. But don't go looking in the internet and just sit down somewhere, ideally a corner or even just lie down on your bed and look up to your ceiling. Close your eyes if that helps and think. Deeply reflect into your life. What has made you smile today? What were the things that made you most happy throughout the years of your life? Are you still continuing to do those things? Are you truly happy at this point in life?

These are but a few questions to think about. When it comes to one's purpose in life though, it gets a little more tricky because it can lead you to a never ending spiral of questions which are overwhelming and really pointless to think about if you aren't sure of yourself yet so let's set that aside for now.

Now after you find answers for the above questions, you can put yourself under the categories of self aware and not so self aware based on the following answers. If your answers include food items, clothing, really any other material object, F1 merch and so on, then I'm sorry to disappoint but you belong to the latter category. Now if your answers are related people (any one in particular or a group of friends or family), nature, animals and acts of kindness then congratulations your are self aware, I now present you with (drumrolls please) this one of a kind self awareness award- a little more awareness about your self awareness XO.

Ok so there are obviously going to be a lot of people who protest this categorisation cuz eating a krispy kreme donut can make you cry happy tears and so can watching a k-drama, thats totally fair and I get it. This brings me to secret category three which is the grey area between the other two categories- the 'I'm self aware but also need to live in delulu land during breaks' category. I myself belong to this one and I believe this is the most superior category cuz we derive happiness from the tiniest of things too.

Now now now, I feel like I'm branching out, let's get back on track here. True happiness is the kind that the self aware people feel, the other stuff from rom-coms and new york cheescakes, will sure make you happy but only while it lasts. Now there are exceptions to this, of course, nothing in this world is definite, everything has loopholes and limitations so don't come at me, all of this stuff is just simply my opinion so hold your horses.

Okay, so far we and by that I mean I , have established that happiness is only derived from living things and well nature which is also mostly living. And through that I would like to establish that happiness only comes through love. A human being is only capable of loving other humans and animals and nature, to the fullest, unconditionally. those other materials things, sure it gives me a lot of happiness, but the happiness fizzles out after I'm done eating my pasta or watching my sitcom. Nature on the other hand never ceases to amaze you nor do people.

So to sum it up, don't go looking for happiness in material things, don't ignore the people around you. Interact more with family and friends and don't let lost in the endless cycle of doomscrolling. Find love, not just romantically but also with your friends. Explore nature and go on treks, do adventurous things and get a dog. All of the above will improve your life for the better. Ok enough of life advice for now. toodles.`,
    mood: 'orange',
    icon: <Lightbulb size={24} />,
    imageUrl: '/src/images/what is happiness.jpg',
    readTime: '5 min read'
  }
];

const ThoughtsCards = () => {
  const [selectedThought, setSelectedThought] = useState<Thought | null>(null);

  const openThought = (thought: Thought) => {
    setSelectedThought(thought);
  };

  const closeThought = () => {
    setSelectedThought(null);
  };

  const getGradientClass = (mood: string) => {
    switch (mood) {
      case 'blue': return 'gradient-blue';
      case 'purple': return 'gradient-purple';
      case 'orange': return 'gradient-orange';
      case 'green': return 'gradient-green';
      default: return 'gradient-blue';
    }
  };

  return (
    <section id="thoughts" className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="text-center mb-12">
        <h2 className="poetry-title text-3xl md:text-4xl mb-4 text-primary">
          Thoughts & Reflections
        </h2>
        <p className="royal-text text-lg text-muted-foreground max-w-2xl mx-auto">
          Deeper dives into life, philosophy, and the random musings that keep me up at night
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {sampleThoughts.map((thought) => (
          <article
            key={thought.id}
            className={`poetry-card cursor-pointer group overflow-hidden pulse-on-hover ${getGradientClass(thought.mood)}`}
            onClick={() => openThought(thought)}
          >
            <div className="relative h-48 overflow-hidden">
              <img
                src={thought.imageUrl}
                alt={thought.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
              <div className="absolute bottom-4 left-4 text-white">
                {thought.icon}
              </div>
            </div>
            
            <div className="p-6">
              <div className="flex items-center text-sm text-muted-foreground mb-3">
                <Calendar size={16} className="mr-2" />
                {thought.date}
                <span className="mx-2">•</span>
                {thought.readTime}
              </div>
              
              <h3 className="poetry-title text-xl mb-3 text-primary">
                {thought.title}
              </h3>
              
              <p className="royal-text text-foreground leading-relaxed mb-4 overflow-hidden" style={{
                display: '-webkit-box',
                WebkitLineClamp: 4,
                WebkitBoxOrient: 'vertical'
              }}>
                {thought.preview}
              </p>
              
              <div className="flex items-center text-sm text-muted-foreground">
                <BookOpen size={16} className="mr-2" />
                Click to read more
              </div>
            </div>
          </article>
        ))}
      </div>

      {/* Thought Modal */}
      {selectedThought && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="poetry-card max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="relative">
              <img
                src={selectedThought.imageUrl}
                alt={selectedThought.title}
                className="w-full h-64 object-cover"
              />
              <button
                onClick={closeThought}
                className="absolute top-4 right-4 btn-poetry bg-white/90 hover:bg-white text-primary w-10 h-10 flex items-center justify-center"
              >
                ×
              </button>
            </div>
            
            <div className="p-8">
              <div className="flex items-center text-sm text-muted-foreground mb-4">
                <Calendar size={16} className="mr-2" />
                {selectedThought.date}
                <span className="mx-2">•</span>
                {selectedThought.readTime}
              </div>
              
              <h3 className="poetry-title text-2xl md:text-3xl mb-6 text-primary">
                {selectedThought.title}
              </h3>
              
              <div className="royal-text text-lg leading-relaxed whitespace-pre-line text-foreground">
                {selectedThought.content}
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default ThoughtsCards;
